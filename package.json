{"name": "invest-qqq-calculator", "version": "0.1.1", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint"}, "prisma": {"seed": "node prisma/seed.js"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.1.0", "@mui/x-date-pickers": "^8.4.0", "@prisma/client": "^5.x.x", "cheerio": "^1.0.0", "date-fns": "^4.1.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "next": "15.3.2", "react": "^19.0.0", "react-countup": "^6.5.3", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "prisma": "^5.x.x", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}